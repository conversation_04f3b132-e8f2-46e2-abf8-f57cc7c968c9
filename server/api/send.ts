import {Resend} from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const {name, email, subject, message} = body;

        const data = await resend.emails.send({
            from: '<EMAIL>',
            to: [process.env.MAIL_TO as string],
            subject: `Portfolio İletişim Formu - ${subject || 'Yeni Mesaj'}`,
            html: `
                <h2>Portfolio İletişim Formu</h2>
                <p><strong>İsim:</strong> ${name}</p>
                <p><strong>E-posta:</strong> ${email}</p>
                <p><strong>Konu:</strong> ${subject}</p>
                <p><strong>Mesaj:</strong></p>
                <div style="white-space: pre-line; background: #f5f5f5; padding: 10px; border-radius: 5px;">${message}</div>
            `,
            reply_to: email || undefined,
        });

        if (data.error)
            throw new Error(`Resend API Error: ${data.error.message}`);

        return {success: true, data};
    } catch (error) {
        console.error('Email sending error:', error);
        throw createError({
            statusCode: 500,
            statusMessage: error instanceof Error ? error.message : 'E-posta gönderilirken hata oluştu'
        });
    }
});