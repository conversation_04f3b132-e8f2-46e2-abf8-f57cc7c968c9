<template>
  <div class="flex flex-col gap-6">
    <h1 class="text-3xl font-bold"><PERSON><PERSON><PERSON></h1>
    <TimeLine :items="projects">
      <template #default="{dataItem}">
        <div class="rounded-lg flex flex-col items-center border-2 border-current/40 p-4 gap-6 hover:shadow-lg
                    dark:shadow-gray-200/20 dark:bg-gray-800/30 bg-inherit sm:flex-row bg-radial-[at_0%_0%]
                    dark:from-blue-500/30 from-sky-400/30 to-60%">
          <div class="flex flex-col gap-4">
            <h2 class="text-xl font-bold group cursor-pointer">
              <a :href="dataItem.link" target="_blank" class="flex items-center gap-2">
                {{ t(`projects.${dataItem.slug}.name`) }}
                <Icon class="pointer-coarse:visible group-hover:visible invisible" icon="mdi:open-in-new"/>
              </a>
            </h2>
            <p v-html="marked.parseInline(t('projects.'+dataItem.slug+'.description'))"/>
            <p class="flex gap-2 flex-wrap">
            <span
                class="text-sm text-center p-1 border font-bold border-blue-600 bg-blue-100 text-blue-600 rounded-lg"
            >
              {{
                dataItem.info.isPersonalProject ?
                    t("projects.personal") :
                    `${t(`projects.company`)} - ${dataItem.info.companyName}`
              }}
            </span>
              <a
                  v-if="dataItem.repoLink"
                  :href="dataItem.repoLink"
                  class="text-sm text-center p-1 border font-bold bg-black text-white rounded-lg flex items-center"
                  target="_blank"
              >
                <Icon icon="line-md:github"/>
                GitHub
              </a>
              <span
                  v-for="tag in dataItem.info.tags"
                  class="text-sm text-center p-1 border font-bold border-green-600 bg-green-100 text-green-600 rounded-lg"
              >
              {{ tag }}
            </span>
            </p>
          </div>
          <div class="min-w-28 max-w-36 flex items-center justify-center">
            <img
                :src="`/project-icons/${dataItem.icon}`"
                :alt="`${t(`projects.${dataItem.slug}.name`)}`"
                class="w-full"
            >
          </div>
        </div>
      </template>
    </TimeLine>
  </div>
</template>

<script setup>
import projects from "~/data/projects.json"
import {Icon} from '@iconify/vue';
import {marked} from 'marked';

const {t} = useI18n();

useHead({
  title: () => ("Abdullah Tunçer | " + t('menu.projects'))
})

</script>

<style scoped>

</style>