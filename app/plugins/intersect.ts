import {defineNuxtPlugin} from '#app'
import {useIntersectionObserver} from '@vueuse/core'

export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.vueApp.directive('intersect', {
        mounted(el: HTMLElement, binding) {
            const options = binding.value || {};
            const {once = true, threshold = 0.2, className = 'show'} = options;

            const {stop} = useIntersectionObserver(
                el,
                (entries) => {
                    const entry = entries[0]
                    if (entry?.isIntersecting) {
                        el.classList.add(className)
                        if (once) stop()
                    } else if (!once) {
                        el.classList.remove(className)
                    }
                },
                {threshold}
            )
        },
        getSSRProps (binding, vnode) {
            // you can provide SSR-specific props here
            return {}
        }
    })
})