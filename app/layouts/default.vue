<template>
  <div class="min-h-screen flex flex-col">
    <header
        class=" sticky top-0 z-50 transition-colors duration-300 bg-stone-200 text-stone-700 dark:text-gray-100
              dark:bg-gray-800 px-4 py-2 flex flex-col"
    >
      <div class="flex justify-end items-center gap-2 sm:gap-4">
        <NuxtLinkLocale to="/" class="mr-auto">
          <NuxtImg src="/favicon.svg" alt="Anasayfa" width="33" height="33"/>
        </NuxtLinkLocale>
        <nav class="hidden sm:block">
          <ul class="flex gap-4">
            <li
                v-for="item in menu"
                :key="item.path"
                class="border-b-2 border-b-transparent hover:border-b-inherit transition-[border-color] duration-300 flex items-center gap-2 p-1"
            >
              <NuxtLinkLocale :to="item.path" class="flex items-center gap-2">
                <Icon :icon="item.icon" class="size-5"/>
                {{ t(item.name) }}
              </NuxtLinkLocale>
            </li>
          </ul>
        </nav>
        <div class="flex items-center gap-2">
          <button
              class="rounded-[50%] dark:border-stone-100 border-stone-700 border-2 box-border cursor-pointer"
              aria-label="Türkçe / English"
              @click="switchLang"
          >
            <Icon
                v-if="locale=='tr'"
                icon="circle-flags:lang-tr"
                class="size-8"
            />
            <Icon
                v-else
                icon="circle-flags:lang-en"
                class="size-8"
            />
          </button>
          <button @click="changeTheme" :aria-label="t('menu.aria.theme')" class="cursor-pointer">
            <Icon
                v-if="$colorMode.preference=='light'"
                icon="line-md:moon-filled-to-sunny-filled-loop-transition"
                class="size-8"
            />
            <Icon
                v-else
                icon="line-md:sunny-filled-loop-to-moon-filled-loop-transition"
                class="size-8"
            />
          </button>
        </div>
        <button
            @click="isOpen = !isOpen"
            class="sm:hidden p-1 rounded-md cursor-pointer"
            :aria-label="t('menu.aria.toggleMenu')"
        >
          <Icon v-if="!isOpen" icon="line-md:close-to-menu-transition" class="size-6"/>
          <Icon v-else icon="line-md:menu-to-close-transition" class="size-6"/>
        </button>
      </div>
      <transition name="slide-fade">
        <nav v-if="isOpen" class="sm:hidden bg-stone-200 dark:bg-gray-800">
          <ul class="flex flex-col gap-2 p-4">
            <li
                v-for="item in menu"
                :key="item.path"
                class="border-b-2 border-b-transparent hover:border-b-inherit transition-[border-color] duration-300"
            >
              <NuxtLinkLocale :to="item.path" class=" flex justify-center gap-3 p-2">
                <Icon :icon="item.icon" class="size-6"/>
                {{ t(item.name) }}
              </NuxtLinkLocale>
            </li>
          </ul>
        </nav>
      </transition>
    </header>
    <main class=" transition-colors duration-300 flex-1 p-6 dark:bg-gray-700 dark:text-white text-stone-600
                bg-stone-100 flex flex-col items-center overflow-x-hidden">
      <div class="max-w-screen-lg w-full">
        <NuxtPage/>
      </div>
    </main>
    <footer class="dark:bg-gray-800 dark:text-white text-gray-800 bg-stone-100 text-center">
      &copy; 2025 Abdullah Tunçer
    </footer>
  </div>
</template>

<script setup lang="ts">
import {Icon} from '@iconify/vue';

const colorMode = useColorMode();
const {t, locale, setLocale} = useI18n();

const isOpen = ref(false);

const menu = computed(() => [
  {name: 'menu.home', path: '/', icon: "ant-design:home-filled"},
  {name: 'menu.aboutMe', path: '/about', icon: "ant-design:user-outlined"},
  // {name: 'menu.blog', path: '/blog', icon: "mdi:blog"},
  {name: 'menu.projects', path: '/projects', icon: "ant-design:fund-projection-screen-outlined"},
  {name: 'menu.contact', path: '/contact', icon: "mdi:contact-mail"}
]);

const switchLang = () => {
  const lang = locale.value == "tr" ? "en" : "tr";
  setLocale(lang);
}

const changeTheme = () => {
  if (colorMode.preference == "dark")
    colorMode.preference = "light";
  else
    colorMode.preference = "dark";
}
</script>

<style scoped>
.slide-fade-enter-active {
  transition: all 0.5s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(-1rem);
  opacity: 0;
}
</style>