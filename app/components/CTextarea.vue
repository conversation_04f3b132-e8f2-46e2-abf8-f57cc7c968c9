<template>
  <div class="flex flex-col w-full gap-1">
    <textarea
        v-model="model"
        :id="id"
        :aria-describedby="descriptionId"
        :aria-invalid="!!description"
        :class="[{'border-stone-300': !description}, {'border-red-500': !!description}]"
        class=" peer order-2 w-full px-4 py-2 bg-stone-50 border rounded-md text-black focus:outline-none
                focus:ring-2 focus:ring-sky-500 focus:border-sky-500 focus:shadow-lg focus:shadow-sky-300/30
                transition duration-300 ease-in-out"
        rows="4"
    />
    <label :for="id" class="order-1 font-medium peer-focus:text-shadow-md text-shadow-sky-300/30">
      {{ props.label }} <span v-if="required" class="font-bold">*</span>
    </label>
    <p :id="descriptionId" class="text-sm text-red-500 order-3">
      {{ description }}
    </p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  id?: string;
  label: string;
  description?: string;
  required?: boolean;
}

const model = defineModel<string>();
const props = defineProps<Props>();

const id = props.id ?? useId();

const descriptionId = computed(() => `${id}-desc`);

</script>

<style scoped>

</style>