{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify/vue": "^5.0.0", "@nuxt/content": "^3.6.3", "@nuxt/image": "^1.11.0", "@nuxtjs/i18n": "^10.0.5", "@tailwindcss/vite": "^4.1.12", "@vueuse/core": "^13.6.0", "better-sqlite3": "^12.2.0", "marked": "^16.2.0", "nuxt": "^4.0.3", "resend": "^6.0.1", "tailwindcss": "^4.1.12", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@nuxt/fonts": "^0.11.4", "@nuxtjs/color-mode": "^3.5.2"}}