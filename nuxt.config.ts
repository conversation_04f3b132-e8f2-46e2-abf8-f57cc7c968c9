// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
  devServer: {
    host: '0.0.0.0',
    port: 3000
  },
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },
  modules: [
    '@nuxt/content',
    '@nuxt/image',
    '@nuxtjs/color-mode',
    '@nuxtjs/i18n',
    '@nuxt/fonts'
  ],
  colorMode: {
    classSuffix: ''
  },
  i18n: {
    strategy: 'prefix_except_default',
    locales: [
      { code: 'tr', name: 'Türkçe', file: 'tr.json' },
      { code: 'en', name: 'English', file: 'en.json' }
    ],
    defaultLocale: 'tr',
    langDir: 'locales/',
    detectBrowserLanguage: {
      useCookie: true,
      cookie<PERSON>ey: 'i18n_redirected',
      redirectOn: 'root' // recommended
    }
  },
  fonts: {
    families: [
      { name: '<PERSON><PERSON><PERSON>', provider: 'google' },
      { name: '<PERSON>', provider: 'google' }
    ]
  }
})