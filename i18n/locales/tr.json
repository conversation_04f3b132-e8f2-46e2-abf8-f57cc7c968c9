{"menu": {"home": "<PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>", "blog": "Blog", "aboutMe": "Hakkımda", "contact": "İletişim", "aria": {"theme": "Temayı Değiştir", "toggleMenu": "Menüyü Aç/Kapat"}}, "home": {"hi": "<PERSON><PERSON><PERSON><PERSON>!", "title": "<PERSON> ", "name": "<PERSON>", "degree": "Frontend Geliştirici - <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Hayata modern ve yaratıcı arayüzler katan bir Frontend geliştiriciyim. \nAmacım; kullanıcıların severek kullandığı, hızlı ve etkili web deneyimleri oluşturmak.", "mail": "E-Posta", "projects": "<PERSON><PERSON><PERSON>", "aria": {"portfolio": "Portföy İkonu", "todo": "<PERSON><PERSON>", "envantr": "EnvanTR İkonu", "pdfReader": "PDF Okuyucu İkonu", "zirveImzaAraci": "Zirve İmza Aracı İkonu", "eIcrapro": "e-İcraPro İkonu", "icraproweb": "İcraProWeb İkonu"}}, "aboutMe": {"intro": "<PERSON><PERSON><PERSON><PERSON>, ben **<PERSON>**. 2021 yılında Süleyman Demirel Üniversitesi **[<PERSON><PERSON><PERSON><PERSON><PERSON>](https://muhendislik.sdu.edu.tr/bilmuh)** bölümünden mezun oldum. Öğrencilik dönemimden itibaren yazılım geliştirmeye ilgi duydum ve özellikle **Frontend geliştirme** alanında kendimi geliştirmeye odaklandım.", "career": "Profesyonel iş <PERSON>ımda, <PERSON><PERSON><PERSON> **Vue.js ekosistemi** üzerinde çalıştım. Bu süreçte **icra hukuku** alanında faaliyet gösteren projelerde görev aldım ve büro yönetimi, icra takip sistemleri ile **[UYAP](https://www.uyap.gov.tr/)** entegrasyonlu otomasyon çözümleri geliştirdim. Bu projeler, yüksek güvenlik ve veri bütünlüğü gerektirdiğinden hem teknik becerilerimi hem de problem çözme yeteneğimi ileriye taşıdı. Ayrıca, **Electron** kullanarak masaüstü uygulamaları geliştirme konusunda da deneyim kazandım.", "projects": "Bireysel projelerimde ise farklı teknolojiler üzerinde kendimi geliştirmeye odaklandım. Örneğin, **Capacitor.js** kullanarak mobil envanter yönetim sistemi geliştirdim. Bu proje sayesinde hem **offline çalışma mantığı** hem de **mobil cihazlara özel kullanıcı deneyimi** tasarımı konularında tecrübe edindim. Ayrıca, **Nuxt 4** üzerinde projeler geliştirerek **[SSR](https://nuxt.com/docs/4.x/getting-started/introduction#server-side-rendering)** konusundaki eksikliklerimi kapatıyorum. Bunun yanında, modern frontend dünyasında önemli bir yer edinen **React ekosistemi** üzerine çalışarak bilgi birikimimi çeşitlendirmeyi hedefliyorum.", "goals": "<PERSON>u anda öncelikli hedeflerim arasında, **test yazımı** ve **test odaklı geliştirme** konusunda kendimi geliştirmek yer alıyor. Bu alanda yetersiz olduğumu düşündüğüm için <PERSON> **unit test**, **integration test** ve **end-to-end test** pratiklerini öğrenerek profesyonel yaklaşımımı daha sağlam bir temele oturtmayı amaçlıyorum.", "closing": "Teknik becerilerimin yanı sıra, **öğrenmeye ve yeni teknolojilere hızlı adapte olmaya** büyük önem veriyorum. **Kod kalitesi**, **ölçeklenebilirlik** ve **kullanıcı deneyimi** gibi konularda titiz bir yaklaşım sergiliyorum. İlerleyen dönemde hedefim, **Vue**, **React** ve **test odaklı geliştirme** alanlarında edindiğim tecrübeleri birleştirerek daha geniş çaplı projelerde yer almak ve yazılım sektöründe kalıcı bir değer üretmek.", "techs": {"title": "Tek<PERSON>lojiler", "description": "Aşağıdaki teknolojiler üzerinde deneyim sa<PERSON>biyim. Karmaşık durumlarda dokümantasyon ve referansları kullanarak hızlı çözüm üretebiliyorum.", "other": "<PERSON><PERSON><PERSON>", "tools": "Araçlar", "aiTools": "AI Araçlar"}, "eduExp": {"title": "Eğitim & Deneyim", "items": [{"isEdu": false, "link": "https://zirve-bilgisayar.com/", "title": "Zirve Bilgisayar Ltd. Şti.", "position": "Frontend Geliştirici", "time": "Ağu 2021 - Kas 2024", "description": "Zirve <PERSON>’da **hukuk ve icra otomasyonu alanında yazılım projeleri** geliştiren ekipte **Frontend Developer** olarak görev aldım. Teknik geliştirmelerin yanı sıra, **ekip çalışması, problem çözme ve kurumsal ortama uyum** gibi yönlerde de önemli deneyimler kazandım.\n\n**Öne Çıkan Katkılar ve Kazanımlar:**", "list": ["**Ekip Çalışması ve İletişim:** Backend geliştiriciler, ürün sahipleri ve diğer ekip üyeleriyle yakın çalışarak müşteri ihtiyaçlarına uygun çözümler geliştirdim.", "**<PERSON><PERSON><PERSON>:** Hem mevcut projelerde bakım ve iyileştirme görevlerinde hem de yeni ürün geliştirmelerinde aktif rol alarak farklı gereksinimlere hızlı şekilde adapte oldum.", "**S<PERSON><PERSON>ç <PERSON>lılık:** Versiyon kontrol, code review ve görev yönetim araçlarını kullanarak düzenli bir geliştirme sürecine katkıda bulundum.", "**Problem Çözme:** Mevcut hukuk otomasyonu sistemlerinde darboğazları tespit ederek pratik çözümler önerdim.", "**S<PERSON>rekli <PERSON>ğrenme:** Vue.js alanındaki yetkinliğimi güçlendirirken, Electron, Express ve Bitbucket Pipelines ile CI/CD süreçleri gibi farklı teknolojilerde de deneyim kazandım.", "**Sorumluluk Alma:** Yeni özelliklerin geliştirilmesinin yanı sıra uygulamaların kararlı çalışması ve sorunsuz yayına alınmasında da sorumluluk üstlendim."]}, {"isEdu": true, "link": "https://muhendislik.sdu.edu.tr/bilmuh", "title": "<PERSON><PERSON><PERSON><PERSON> Demirel <PERSON>niversitesi", "position": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "Eyl 2017 - Ağu 2021", "description": "Lisans eğitimim süresince zorunlu stajlarımı tamamladım. 2021 yılında Bilgisayar Mühendisliği Lisans derecesi ile mezun oldum", "list": ["**GPA:** 3.23 / 4"]}, {"isEdu": false, "link": "https://zirve-bilgisayar.com/", "title": "Zirve Bilgisayar Ltd. Şti.", "position": "Yaz<PERSON><PERSON>ım <PERSON>ajı", "time": "Tem 2021 - Ağu 2021", "description": "Yazılım stajım süresince Vue.js ve modern front-end teknolojileri üzerine odaklandım. Vue CLI, Vue Router, Vuetify ve component tabanlı mimariyi kullanarak dinamik ve responsive kullanıcı arayüzleri geliştirdim. REST API entegrasyonu, form işlemleri ve Git sürüm kontrol sistemini aktif olarak kullandım. Takım içinde kod review, merge işlemleri ve hata giderme süreçlerine dahil olarak profesyonel yazılım geliştirme pratiklerini deneyimledim.", "list": ["**Borçlu & Alacaklı Yönetim Sistemi:** Vue.js ve Vuetify ile kart tabanlı liste ve detay görünümleri geliştirdim.", "**Form Yönetimi & Validasyon:** <PERSON><PERSON><PERSON> formlar, input validation ve kullanıcı etkileşimleri üzerine çalıştım.", "**Responsive Tasarım:** Mobil ve tablet uyumlu arayüzler tasarladım ve grid sistemlerini kullandım.", "**API Entegrasyonu:** REST API ile veri çekme, gönderme ve state yönetimi yaptım.", "**Git & Bitbucket:** Branch yönetimi, merge, conflict çözümleme ve takım halinde kod paylaşımı deneyimledim.", "**Optimizasyon & Debugging:** Performans iyileştirmeleri ve hata giderme süreçlerinde rol aldım."]}]}}, "projects": {"personal": "<PERSON><PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "portfolio": {"name": "Kişisel Portföy", "description": "Kendi çalışmalarımı ve projelerimi sergilemek için geliştirdiğim kişisel portföy sitem, Nuxt 4 kullanarak oluşturduğum ilk SSR (Server-Side Rendering) projesidir.\n\nBu proje sayesinde:\n - **SSR mantığını** (hız, SEO uyumu, ilk yükleme performansı) deneyimledim,\n - **i18n entegrasyonu** ile çok dilli içerik hazırlamayı öğrendim,\n - Tasarımda Tailwind CSS ile modern, minimalist bir stil uyguladım.\n\nPortföy sitesi, hem iş arayışında kendimi tanıtmak için bir araç, hem de yeni teknolojileri öğrenme sürecimde önemli bir adım oldu."}, "todo": {"name": "Todo", "description": "Bu proje, React (TypeScript) ve Tailwind CSS ile geliştirdiğim modern ve kullanıcı dostu bir todo uygulamasıdır. Vite sayesinde hızlı geliştirme süreci yaşanırken, Dexie.js ile IndexedDB'de güvenli veri depolaması sağlanmıştır.\n\nUygulama şu özellikleri içerir:\n - Görev oluşturma, d<PERSON><PERSON><PERSON>e, silme\n - Renk kodlaması ile görsel kategorilendirme\n - Favorilere ekleme ve ilerleme çubuğu\n - Görev arama özelliği\n - Responsive tasarım (mobil + masaüstü uyumlu)\n\nBu proje sayesinde React ekosistemine (TypeScript, Vite, Dexie.js, Tailwind CSS) dair tecrübeler edindim."}, "envantr": {"name": "EnvanTR", "description": "Gerçek bir ihtiyacı çözmeye odaklanarak geliştirdiğim ilk mobil uygulamamdır. Vue 3 ve Capacitor kullanarak hayata geçirdim. Uygulama;\n - Stokta bulunan ürünleri takip etme,\n - Stok geçmişini görüntüleme,\n - Barkod taratarak ürün arama,\n - Barkodu olmayan ürünlere yeni barkod oluşturma,\n - Ürünleri tarayarak satış listesi oluşturma ve indirim uygulama\n gibi özellikler sunar.\n\nBu projede ayrıca:\n - Pinia ile modern state yönetimini deneyimledim,\n - Vitest ile ilk kez unit test yazma deneyimi edindim.\n\nBu sayede hem Vue 3’ün mobil tarafta kullanımını hem de test süreçlerini öğrenme fırsatı buldum."}, "pdfReader": {"name": "PDF Okuyucu", "description": "İş hayatımda sürekli Vue 2 kullandığım için yeni versiyona (Vue 3 + Composition API) geçişi deneyimlemek amacıyla bireysel olarak geliştirdiğim bir projedir. Uygulama, Web Speech API kullanarak yüklenen PDF dosyalarını sesli şekilde okur.\n\n**Başlıca özellikler:**\n - **Vue 3 Composition API** öğrenme sürecine yönelik geliştirme,\n - PDF dosyalarını **Web Speech API** ile seslendirme,\n - Başlangıçta mobil hedeflense de, API kısıtlamaları nedeniyle **masaüstünde stabil** çalışır hale getirildi.\n\nBu proje sayesinde Vue 3’ün yeni yaklaşımını deneyimleme ve tarayıcı API’lerini kullanarak farklı bir kullanıcı deneyimi oluşturma fırsatı buldum."}, "zirveImzaAraci": {"name": "Zirve İmza <PERSON>", "description": "Yeni Eicrapro’nun çalışabilmesi için ihtiyaç duyulan imzalama aracı, Electron ve Vue ile masaüstü ortamında geliştirildi. Socket.io entegrasyonu sayesinde uygulamalar arası anlık iletişim sağlandı.\n\n**Projeye katkılarım:**\n - Başlangıçtan itibaren geliştirme sürecinde rol aldım.\n - **Socket.io** kullanarak Eicrapro ile masaüstü uygulaması arasında **gerçek zamanlı iletişim** kurdum.\n - **Debug ve test süreçlerinde** görev alarak uygulamanın kararlı çalışmasını sağladım.\n - <PERSON>y<PERSON>lamanın, Eicrapro üzerinden gönderilen görevleri işleyip imzalama sürecine dahil olmasını sağlayacak modülleri geliştirdim."}, "eIcrapro": {"name": "e-İcraPro", "description": "<PERSON><PERSON><PERSON><PERSON>, kullanıcıların UYAP üzerinden gerçekleştirdiği takip açma, dosya gönderme ve taraf bilgilerini alma gibi işlemleri otomatikleştiren bir web uygulamasıdır. Bu sayede kullanıcılar manuel işlemler yerine toplu, hızlı ve hatasız şekilde süreçlerini tamamlayabilmektedir.\n\n**Projeye katkılarım:**\n - Eski uygulamada debug ve küçük geliştirmeler gerçekleştirdim.\n - Yeni uygulamada uygulama mimarisinin planlanması, Ar-Ge çalışmaları ve geliştirme süreçlerinde baştan sona aktif rol aldım.\n - Kullanıcıların toplu işlem yapabilmesini kolaylaştıran bileşenler geliştirdim ve raporlama fonksiyonlarının performansını artırmaya yönelik çözümler ürettim."}, "icraproweb": {"name": "İcraProWeb", "description": "İcrapro masaüstü uygulamasının, modern teknolojilerle web ortamına taşınmış versiyonudur. Proje kapsamında ihtiyaç analizi, komponent geli<PERSON><PERSON>rm<PERSON>, Ar-Ge ve hata ayıklama süreçlerinde görev aldım.\n\n**Uygulama içerisinde:**\n - Yönet<PERSON>lerin, satın aldıkları pakete göre avukat ve personel ekleyebilmesi ve yetkilendirmesi,\n - Vekalet atama ve iş dağıtımı yapılabilmesi,\n - İcra takipleri ile alacaklı faiz hesaplamalarının gerçekleştirilebilmesi sağlandı.\n\nBu süreçte kullanıcı deneyimini güçlendiren bileşenler geliştirdim ve performans sorunlarına yönelik çözümler ürettim."}}, "contact": {"name": "İsim", "email": "E-Posta", "subject": "<PERSON><PERSON>", "message": "<PERSON><PERSON>", "location": "<PERSON><PERSON>", "rules": {"nameRequired": "<PERSON><PERSON> <PERSON><PERSON>.", "emailInvalid": "Geçerli bir e-posta adresi giriniz.", "messageRequired": "<PERSON><PERSON> <PERSON><PERSON>."}, "send": "<PERSON><PERSON><PERSON>", "sendSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "sendFail": "<PERSON><PERSON> bir <PERSON>."}}