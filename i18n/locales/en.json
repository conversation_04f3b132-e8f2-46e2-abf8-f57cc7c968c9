{"menu": {"home": "Home", "projects": "Projects", "blog": "Blog", "aboutMe": "About Me", "contact": "Contact", "aria": {"theme": "Change Theme", "toggleMenu": "Toggle <PERSON>"}}, "home": {"hi": "Hi There!", "title": "I'm ", "name": "<PERSON>", "degree": "<PERSON><PERSON> Developer - Software Engineer", "description": "I am a Frontend developer who adds modern and creative interfaces to life. My goal is to create fast and efficient web experiences that users love to use.", "mail": "Email", "projects": "Projects", "aria": {"portfolio": "Portfolio Icon", "todo": "Todo Icon", "envantr": "EnvanTR Icon", "pdfReader": "PDF Reader Icon", "zirveImzaAraci": "Zirve Signature Tool Icon", "eIcrapro": "e-İcraPro Icon", "icraproweb": "İcraProWeb Icon"}}, "aboutMe": {"intro": "Hello, I'm **<PERSON>**. I graduated from Süleyman Demirel University in 2021 with a degree in **[Computer Engineering](https://muhendislik.sdu.edu.tr/bilmuh)**. Since my student years, I have been interested in software development and focused on improving myself in **frontend development**.", "career": "In my professional career, I have mainly worked within the **Vue.js ecosystem**. During this period, I participated in projects in the field of **enforcement law**, developing office management and enforcement tracking systems integrated with **[UYAP](https://www.uyap.gov.tr/)** automation solutions. These projects required high security and data integrity, enhancing both my technical skills and problem-solving abilities. I also gained experience developing desktop applications using **Electron**.", "projects": "In my personal projects, I focused on developing skills in different technologies. For example, I developed a mobile inventory management system using **Capacitor.js**, gaining experience in **offline functionality** and **mobile-specific user experience design**. Additionally, I develop projects on **Nuxt 4**, improving my skills in **[SSR](https://nuxt.com/docs/4.x/getting-started/introduction#server-side-rendering)**. I also work on the **React ecosystem**, which plays an important role in modern frontend, to diversify my knowledge.", "goals": "My current priority is to improve myself in **writing tests** and **test-driven development**. Considering my lack of experience in this field, I aim to strengthen my professional approach by learning **unit tests**, **integration tests**, and **end-to-end tests**.", "closing": "Besides technical skills, I place great importance on **learning and adapting quickly to new technologies**. I approach **code quality**, **scalability**, and **user experience** with meticulous attention. In the future, my goal is to combine my experiences in **Vue**, **React**, and **test-driven development** to work on larger projects and create lasting value in the software industry.", "techs": {"title": "Technologies", "description": "I have experience with the following technologies. In complex situations, I can quickly produce solutions using documentation and references.", "other": "Other", "tools": "Tools", "aiTools": "AI Tools"}, "eduExp": {"title": "Education & Experience", "items": [{"isEdu": false, "link": "https://zirve-bilgisayar.com/", "title": "Zirve Bilgisayar Ltd. Şti.", "position": "Frontend Developer", "time": "Aug 2021 - Nov 2024", "description": "I worked as a **Frontend Developer** in a team developing software projects in **legal and enforcement automation** at Zirve Bilgisayar. Beyond technical development, I gained significant experience in **teamwork, problem-solving, and adapting to corporate environments**.\n\n**Key Contributions and Achievements:**", "list": ["**Teamwork & Communication:** Collaborated closely with backend developers, product owners, and other team members to develop solutions that meet client needs.", "**Adaptability:** Actively participated in both maintenance/improvement of existing projects and development of new products, quickly adapting to diverse requirements.", "**Process-Oriented:** Contributed to a structured development process using version control, code reviews, and task management tools.", "**Problem Solving:** Identified bottlenecks in existing legal automation systems and proposed practical solutions.", "**Continuous Learning:** Strengthened Vue.js proficiency while gaining experience with Electron, Express, and CI/CD processes via Bitbucket Pipelines.", "**Taking Responsibility:** Took ownership not only for developing new features but also ensuring stable application performance and smooth deployment."]}, {"isEdu": true, "link": "https://muhendislik.sdu.edu.tr/bilmuh/en", "title": "S<PERSON>leyman Demirel University", "position": "Computer Engineering", "time": "Sep 2017 - Aug 2021", "description": "Completed mandatory internships during my undergraduate studies. Graduated in 2021 with a Bachelor's degree in Computer Engineering.", "list": ["**GPA:** 3.23 / 4"]}, {"isEdu": false, "link": "https://zirve-bilgisayar.com/", "title": "Zirve Bilgisayar Ltd. Şti.", "position": "Software Intern", "time": "Jul 2021 - Aug 2021", "description": "During my software internship, I focused on Vue.js and modern front-end technologies. I developed dynamic and responsive user interfaces using Vue CLI, Vue Router, Vuetify, and component-based architecture. I actively used REST API integration, form handling, and Git version control. Participated in code reviews, merges, and bug fixing processes within the team to gain professional development experience.", "list": ["**Debtor & Creditor Management System:** Developed card-based list and detail views using Vue.js and Vuetify.", "**Form Management & Validation:** Worked on dynamic forms, input validation, and user interactions.", "**Responsive Design:** Designed mobile and tablet-friendly interfaces using grid systems.", "**API Integration:** Managed data fetching, sending, and state handling via REST API.", "**Git & Bitbucket:** Managed branches, merges, conflict resolution, and team-based code sharing.", "**Optimization & Debugging:** Contributed to performance improvements and bug fixing."]}]}}, "projects": {"personal": "Personal", "company": "Corporate", "portfolio": {"name": "Personal Portfolio", "description": "My personal portfolio website, developed to showcase my work and projects, is my first SSR (Server-Side Rendering) project built with Nuxt 4.\n\nThrough this project I gained experience with:\n - **SSR concepts** such as speed, SEO optimization, and initial load performance,\n - **i18n integration** for building multilingual content,\n - Applying a modern, minimalist style using **Tailwind CSS**.\n\nThis portfolio site has been both a tool to present myself during job opportunities and a significant step in my journey of learning new technologies."}, "todo": {"name": "Todo", "description": "This project is a modern, user-friendly todo application I developed with React (TypeScript) and Tailwind CSS. Thanks to Vite, the development process was fast, while Dexie.js enabled secure data storage in IndexedDB.\n\nThe application includes the following features:\n - Create, edit, and delete tasks\n - Visual categorization with color coding\n - Add to favorites and track with a progress bar\n - Task search functionality\n - Responsive design (mobile + desktop compatible)\n\nThrough this project, I gained hands-on experience with the React ecosystem (TypeScript, Vite, Dexie.js, Tailwind CSS)."}, "envantr": {"name": "EnvanTR", "description": "This is my first mobile application, developed with a focus on solving a real-world need. I built it using Vue 3 and Capacitor. The app provides features such as:\n - Tracking products in stock,\n - Viewing stock history,\n - Searching products by scanning barcodes,\n - Generating barcodes for items without one,\n - Creating sales lists by scanning products and applying discounts.\n\nIn addition, I:\n - Gained experience with modern state management using **Pinia**,\n - Wrote my first unit tests using **Vitest**.\n\nThis project gave me the opportunity to explore Vue 3 on mobile platforms and gain insights into testing workflows."}, "pdfReader": {"name": "PDF Reader", "description": "Since I had primarily worked with Vue 2 in my professional experience, I developed this project individually to practice with Vue 3 and the Composition API. The application reads uploaded PDF files aloud using the Web Speech API.\n\n**Key features:**\n - Hands-on practice with **Vue 3 Composition API**,\n - Reading PDF files aloud with the **Web Speech API**,\n - Initially planned as a mobile app, but due to API limitations, it was stabilized for **desktop use only**.\n\nThrough this project, I explored Vue 3’s new approach and learned how to leverage browser APIs to create unique user experiences."}, "zirveImzaAraci": {"name": "<PERSON><PERSON>ve Signing Tool", "description": "To enable the new Eicrapro to function, a signing tool was developed as a desktop application using Electron and Vue. Thanks to Socket.io integration, real-time communication between applications was achieved.\n\n**My contributions to the project:**\n - Took part in the development process from the very beginning,\n - Implemented **real-time communication** between Eicrapro and the desktop app using **Socket.io**,\n - Contributed to **debugging and testing processes** to ensure stability,\n - Developed modules responsible for processing tasks sent from Eicrapro and handling the signing workflow."}, "eIcrapro": {"name": "e-IcraPro", "description": "Eicrapro is a web application that automates processes such as initiating enforcement cases, sending files, and retrieving party information through UYAP (National Judiciary Informatics System). It enables users to complete these tasks in bulk, quickly, and accurately instead of manually.\n\n**My contributions to the project:**\n - Performed debugging and small enhancements in the legacy application,\n - Played an active role from start to finish in the planning of the new application’s architecture, R&D, and development processes,\n - Built components that streamlined bulk operations and improved the performance of reporting functionalities."}, "icraproweb": {"name": "IcraProWeb", "description": "IcraProWeb is the modern web-based version of the Icrapro desktop application, rebuilt with up-to-date technologies. In this project, I contributed to requirement analysis, component development, R&D, and debugging.\n\n**Within the application:**\n - Administrators can add lawyers and staff depending on their subscription package and assign permissions,\n - Delegation of power of attorney and task distribution is supported,\n - Enforcement case tracking and interest calculations for creditors can be performed.\n\nDuring this process, I developed components that enhanced user experience and delivered solutions to performance-related challenges."}}, "contact": {"name": "Name", "email": "Email", "subject": "Subject", "message": "Message", "location": "Location", "rules": {"nameRequired": "This field is required.", "emailInvalid": "Please enter a valid email address.", "messageRequired": "This field is required."}, "send": "Send", "sendSuccess": "Message sent successfully.", "sendFail": "There was an issue sending the message."}}